#!/usr/bin/env python
# -*- coding: utf8 -*-
"""
Written by <PERSON><PERSON><PERSON><PERSON><PERSON>@zork.com.cn
"""
import json
import requests
import self_config
from tools import service_instance
from pyVmomi import vim


data = {}
data2 = {}
esxi_arr_total = []

insert_server = {}
insert_cpu = {}
insert_vm = {}


def get_host(content):
    # 物理机 HostSystem
    container = content.viewManager.CreateContainerView(content.rootFolder, [vim.HostSystem], True)
    esxi_obj = [view for view in container.view]
    for esxi in esxi_obj:
        esxi_host_summary_info = {
            "CPU": esxi.summary.hardware.cpuModel,
            "CPUNum": esxi.summary.hardware.numCpuPkgs,
            "CPUNumberOfCores": esxi.summary.hardware.numCpuCores,
            "CPUThreadNum": esxi.summary.hardware.numCpuThreads,
            "memory": int(esxi.summary.hardware.memorySize / 1024 ** 3),
            "memUsed": int(esxi.summary.quickStats.overallMemoryUsage / 1024 if esxi.summary.quickStats.overallMemoryUsage else 0),
            "memFree": int(esxi.summary.hardware.memorySize / 1024 ** 3 - (esxi.summary.quickStats.overallMemoryUsage / 1024 if esxi.summary.quickStats.overallMemoryUsage else 0)),
            "hostname": esxi.name,
            "os": esxi.summary.config.product.fullName,
            "osType": "Esxi",
            "osBit": "64",
            "serialNumber": "",
            "diskSize": "",
            "computermodel": esxi.summary.hardware.model,
            "uuid": esxi.summary.hardware.uuid
        }
        for i in esxi.summary.hardware.otherIdentifyingInfo:
            if isinstance(i, vim.host.SystemIdentificationInfo) and i.identifierType.key == "SerialNumberTag":
                esxi_host_summary_info["serialNumber"] = i.identifierValue
        disk_size = 0
        for ds in esxi.datastore:
            disk_size += int(ds.summary.capacity / 1024 ** 3)
        esxi_host_summary_info["diskSize"] = disk_size
        esxi_arr_total.append(esxi_host_summary_info)
    print(json.dumps(esxi_arr_total, sort_keys=True, indent=4, ensure_ascii=False))


def get_nics(guest):
    nics = {}
    for nic in guest.net:
        if nic.network:  # Only return adapter backed interfaces
            if nic.ipConfig is not None and nic.ipConfig.ipAddress is not None:
                nics[nic.macAddress] = {}  # Use mac as uniq ID for nic
                nics[nic.macAddress]['netlabel'] = nic.network
                ipconf = nic.ipConfig.ipAddress
                i = 0
                nics[nic.macAddress]['ipv4'] = {}
                for ip in ipconf:
                    if ":" not in ip.ipAddress:  # Only grab ipv4 addresses
                        nics[nic.macAddress]['ipv4'][i] = ip.ipAddress
                        nics[nic.macAddress]['prefix'] = ip.prefixLength
                        nics[nic.macAddress]['connected'] = nic.connected
                    i = i+1
    return nics


def vmsummary(summary, guest):
    vmsum = {}
    config = summary.config
    net = get_nics(guest)
    vmsum['uuid'] = str(config.uuid)
    # 虚机总内存大小
    # vmsum['mem'] = str(config.memorySizeMB / 1024)
    vmsum['mem'] = str(config.memorySizeMB)
    vmsum['memUsed'] = str(summary.quickStats.guestMemoryUsage / 1024)
    # vmsum['consumedOverheadMemory'] = str(summary.quickStats.consumedOverheadMemory / 1024)
    # vmsum['distributedMemoryEntitlement'] = str(summary.quickStats.distributedMemoryEntitlement / 1024)  
    # 虚机已使用磁盘空间
    vmsum['diskUsedGB'] = str("%.2f" % (summary.storage.committed / 1024**3))
    # 虚机总置备磁盘空间
    vmsum['diskFreeGB'] = str("%.2f" % (summary.storage.uncommitted / 1024**3))
    # 虚机未共享磁盘空间
    vmsum['diskUnsharedGB'] = str("%.2f" % (summary.storage.unshared / 1024**3))
    # CPU核数
    vmsum['cpu'] = str(config.numCpu)
    # vmsum['distributedCpuEntitlement'] = str(summary.quickStats.distributedCpuEntitlement / 1024)
    # vmsum['staticCpuEntitlement'] = str(summary.quickStats.staticCpuEntitlement / 1024)
    vmsum['path'] = config.vmPathName
    vmsum['ostype'] = config.guestFullName
    vmsum['state'] = summary.runtime.powerState
    vmsum['annotation'] = config.annotation if config.annotation else ''
    vmsum['net'] = net
    return vmsum


def vm2dict(datacenter, cluster, host, vm, summary):
    # If nested folder path is required, split into a separate function
    vmname = vm.summary.config.name
    data[datacenter][cluster][host][vmname]['folder'] = vm.parent.name
    data[datacenter][cluster][host][vmname]['uuid'] = summary['uuid']
    data[datacenter][cluster][host][vmname]['mem'] = summary['mem']
    data[datacenter][cluster][host][vmname]['memUsed'] = summary['memUsed']
    # data[datacenter][cluster][host][vmname]['consumedOverheadMemory'] = summary['consumedOverheadMemory']
    # data[datacenter][cluster][host][vmname]['distributedMemoryEntitlement'] = summary['distributedMemoryEntitlement']
    data[datacenter][cluster][host][vmname]['diskUsedGB'] = summary['diskUsedGB']
    data[datacenter][cluster][host][vmname]['diskFreeGB'] = summary['diskFreeGB']
    data[datacenter][cluster][host][vmname]['diskUnsharedGB'] = summary['diskUnsharedGB']
    data[datacenter][cluster][host][vmname]['cpu'] = summary['cpu']
    # data[datacenter][cluster][host][vmname]['distributedCpuEntitlement'] = summary['distributedCpuEntitlement']
    # data[datacenter][cluster][host][vmname]['staticCpuEntitlement'] = summary['staticCpuEntitlement']
    data[datacenter][cluster][host][vmname]['path'] = summary['path']
    data[datacenter][cluster][host][vmname]['net'] = summary['net']
    data[datacenter][cluster][host][vmname]['ostype'] = summary['ostype']
    data[datacenter][cluster][host][vmname]['state'] = summary['state']
    data[datacenter][cluster][host][vmname]['annotation'] = summary['annotation']


def data2json(raw_data, args):
    with open(args.jsonfile, 'w') as json_file:
        json.dump(raw_data, json_file)


def main():
    si = service_instance.connect(self_config)
    content = si.RetrieveContent()
    get_host(content)
    children = content.rootFolder.childEntity
    for child in children:  # Iterate though DataCenters
        datacenter = child
        data[datacenter.name] = {}  # Add data Centers to data dict
        clusters = datacenter.hostFolder.childEntity
        for cluster in clusters:  # Iterate through the clusters in the DC
            # Add Clusters to data dict
            data[datacenter.name][cluster.name] = {}
            hosts = cluster.host  # Variable to make pep8 compliance

            for host in hosts:  # Iterate through Hosts in the Cluster
                hostname = host.summary.config.name
                serialNum = hostname
                for i in host.summary.hardware.otherIdentifyingInfo:
                    if isinstance(i, vim.host.SystemIdentificationInfo) and i.identifierType.key == "SerialNumberTag":
                        serialNum = i.identifierValue
                # Add VMs to data dict by config name
                # data[datacenter.name][cluster.name][hostname] = {}
                data[datacenter.name][cluster.name][serialNum] = {}
                vms = host.vm
                for vm in vms:  # Iterate through each VM on the host
                    vmname = vm.summary.config.name
                    # data[datacenter.name][cluster.name][hostname][vmname] = {}
                    data[datacenter.name][cluster.name][serialNum][vmname] = {}
                    summary = vmsummary(vm.summary, vm.guest)
                    # vm2dict(datacenter.name, cluster.name, hostname, vm, summary)
                    vm2dict(datacenter.name, cluster.name, serialNum, vm, summary)

    if not self_config.silent:
        print(json.dumps(data, sort_keys=True, indent=4, ensure_ascii=False))

    if self_config.out_put_json:
        data2json(data, self_config)

    if self_config.add_to_cmdb:
        # add_vc_server()
        add_vc_vm()


new_headers = {
    'from': 'Y',
    'Content-Type': 'application/json'
}


# vc 宿主机
def add_vc_server():
    add_inst_url = self_config.cmdb_add_inst_url

    for esxi in esxi_arr_total:
        new_data = {
            "zork_obj_id": "server",
            "zork_inst_name": esxi['hostname'],
            "product_ip": esxi['hostname'],
            "asset_status": 1,
            "create_user_name": "vcenter",
            "asset_model": esxi['computermodel'],
            "serial_number": esxi['serialNumber']
        }
        res = requests.post(url=add_inst_url, headers=new_headers, data=json.dumps(new_data))
        print (json.dumps(res.text, sort_keys=True, indent=4, ensure_ascii=False))
        if json.loads(res.text)['code'] == 0:
            print json.loads(res.text)['data']['zork_inst_id']
            insert_server[esxi['hostname']] = json.loads(res.text)['data']['zork_inst_id']
        else:
            print json.loads(res.text)['msg']


# vc 虚拟机
def add_vc_vm():
    for key1, value1 in data.iteritems():
        for key2, value2 in value1.iteritems():
            for key3, value3 in value2.iteritems():
                for key4, value4 in value3.iteritems():
                    inner_ip = ''
                    inner_mac = ''
                    if value4['net']:
                        print value4['net']
                        # 多mac 多ip的情况
                        i = 0
                        for net_key, net_value in value4['net'].iteritems():
                            inner_mac = net_key
                            if net_value['ipv4'].get(i):
                                inner_ip = net_value['ipv4'].get(i)
                                i = i + 1
                                insert_host_json = {
                                    "zork_supplier_account": "0",
                                    "host_info": {
                                        "0": {
                                            "vcenter_datacenter": key1,
                                            "vcenter_cluster": key2,
                                            "vcenter_hostname": key3,
                                            "zork_host_name": key4,
                                            "zork_host_innerip": inner_ip,
                                            "zork_mac": inner_mac,
                                            "zork_mem": int(float(value4['mem'])),
                                            "zork_cpu": int(float(value4['cpu'])),
                                            "zork_disk": int(float(value4['diskUsedGB'])) + int(float(value4['diskFreeGB'])),
                                            "zork_cloud_id": 0,
                                            "import_from": "3"
                                        }
                                    }
                                }
                                print insert_host_json
                                res = requests.post(url=self_config.cmdb_add_host_url, headers=new_headers, data=json.dumps(insert_host_json))
                                print (json.dumps(res.text, sort_keys=True, indent=4, ensure_ascii=False))
                                if json.loads(res.text)['code'] == 0:
                                    print 'success:'+key4
                                    # 建立虚拟机与宿主机关系 宿主机的hostname
                                    print json.loads(res.text)['data']['success']
                                    print json.loads(res.text)['data']['success'][0]
                                    # 所属物理机插入成功才插入关系 （下面id 换成云新）
                                    if insert_server.get(key3):
                                        # insert_asst = {
                                        #     "zork_obj_id": "server",
                                        #     "zork_inst_id": insert_server.get(key3),
                                        #     "zork_asst_obj_id": "host",
                                        #     "zork_asst_inst_id": json.loads(res.text)['data']['success'][0],
                                        #     "zork_obj_asst_id": "server_contains_host"
                                        # }
                                        insert_asst = {
                                            "zork_obj_id": "zork_server",
                                            "zork_inst_id": insert_server.get(key3),
                                            "zork_asst_obj_id": "host",
                                            "zork_asst_inst_id": json.loads(res.text)['data']['success'][0],
                                            "zork_obj_asst_id": "zork_server_belong_host"
                                        }
                                        res = requests.post(url=self_config.cmdb_add_inst_asst_url, headers=new_headers, data=json.dumps(insert_asst))
                                        if json.loads(res.text)['code'] == 0:
                                            print 'success insert asst'
                                        else:
                                            print 'fail,'+json.loads(res.text)['msg']
                                else:
                                    print 'fail:'+key4+','+json.loads(res.text)['msg']
                    else:
                        print 'fail:net null(ip null),'+key4


# 云新物理机
def add_yx_server():
    get_device_url = self_config.cloudsino_get_device_url
    add_inst_url = self_config.cmdb_add_inst_url
    add_inst_asst_url = self_config.cmdb_add_inst_asst_url

    headers = {
        'Content-Type': 'application/json'
    }
    data = {
        "token": self_config.cloudsino_token,
        "devicetype": 1
    }
    resp = requests.post(url=get_device_url, headers=headers, data=json.dumps(data))

    # 云新 格式的数据
    resp2 = {
        "statuscode":0,
        "message":"验证成功",
        "total":501,
        "deviceList":[
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-1",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"6V04BC2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-2",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"H302GM2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-3",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"JJZNBD2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-4",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"H3Z7GM2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-5",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"BPJLFD2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-6",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"1RPLFD2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-7",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"06FCCL1",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-8",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"06DVFL6",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-9",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"06FCDK2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-10",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"06TGFV6",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-11",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"1SMJBD2",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            },
            {
                "baseInfo":{
                    "app":"DB",
                    "catalog":"/x86服务器/dell",
                    "create_monitor_time":"2020-08-1817:03:12.0",
                    "cpu_count":4,
                    "description":"",
                    "device_status":"已上线2020-08-18",
                    "devicename":"B02C01_5-6U_生产内网_裸金属_数据库_2jx8601n01-12",
                    "disk_status":"1116G,2块",
                    "endU":"6",
                    "fan_count":"6",
                    "field1":"Compute",
                    "field2":"业务区-数据库",
                    "firmwareversion":"**********",
                    "first.monitor_date":"2020-08-18",
                    "frameCode":"co1",
                    "framename":"Co1",
                    "idc":"张江IDC数据中心",
                    "idcCode":"",
                    "ip":"************",
                    "isLicenseKvm":False,
                    "manufacturer":"Dell",
                    "memory-status":"总容量：5126，16条",
                    "model":"PowerEdgeR840",
                    "monitorStatus":"已纳管",
                    "nic_count":3,
                    "off_frame_date":"",
                    "off_frame_desc":"",
                    "offline_date":"",
                    "offline_desc":"",
                    "online_date":"2020-08-1818:09:52.0",
                    "onlinedesc":"0818",
                    "oobversion":"iDRAC9",
                    "os":"",
                    "position_desc":"",
                    "productip":"",
                    "raidcard_count":"1",
                    "raidlevel":"RAID 1",
                    "roomCode":"001",
                    "roomarea":"DB区",
                    "roomname":"张江五楼IDCB02机房",
                    "startU":"5",
                    "servicetag":"9KT7S62",
                    "shape":"机架式",
                    "type":"x86服务器",
                    "ucount":2
                },
                "harewareConf":{
                    "cpuInfo":{
                        "count":4,
                        "cpu":[
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"1",
                                "manufacturer":"Intel",
                                "name":"cpu 1",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            },
                            {
                                "corenumber":"14",
                                "frequency":"2600 MHz",
                                "index":"2",
                                "manufacturer":"Intel",
                                "name":"cpu 2",
                                "partnumber":"",
                                "serialnumber":"",
                                "type":"Intel(R) Xeon(R) Gold 6132 CPU @ 2.60GHz ",
                                "wwn":""
                            }
                        ]
                    }
                }
            }
        ]
    }

    if json.loads(resp.text)['statuscode'] == 200:
        deviceList = json.loads(resp.text)['deviceList']
    # if resp['statuscode'] == 0:
    #     deviceList = resp['deviceList']
        for i in range(len(deviceList)):
            device = deviceList[i]
            # 设备基本信息
            baseinfo = device['baseInfo']
            # new_data = {
            #     "zork_obj_id": "server",
            #     "zork_inst_name": baseinfo['devicename'],
            #     "product_ip": baseinfo['ip'],
            #     # "asset_monitor_status": baseinfo['monitorStatus'],
            #     "create_user_name": "cloudsino",
            #     "asset_model": baseinfo['model'],
            #     "serial_number": baseinfo['servicetag'],
            #     "manufacturer": baseinfo['manufacturer'],
            #     "device_u": baseinfo['ucount'],
            #     "start_u": baseinfo['startU'],
            #     "end_u": baseinfo['endU'],
            #     "device_cabinet": baseinfo['framename'],
            #     "device_room":baseinfo['roomarea'],
            #     "data_center":baseinfo['idc']
            # }
            new_data = {
                "zork_obj_id": "zork_server",
                "zork_inst_name": baseinfo['devicename'],
                "managementIp": baseinfo['ip'],
                # "create_user_name": "cloudsino",
                "assetType": baseinfo['model'],
                "assetSerialNumber": baseinfo['servicetag'],
                "facilityDescriptor": baseinfo['servicetag'],
                "assetBrand": baseinfo['manufacturer'],
                "device_u": baseinfo['ucount'],
                "start_u": baseinfo['startU'],
                "end_u": baseinfo['endU'],
                "belongCabinet": baseinfo['framename'],
                "deployComputerRoom":baseinfo['roomarea'],
                "deployDb":baseinfo['idc']
            }

            res = requests.post(url=add_inst_url, headers=new_headers, data=json.dumps(new_data))
            print (json.dumps(res.text, sort_keys=True, indent=4, ensure_ascii=False))
            if json.loads(res.text)['code'] == 0:
                print json.loads(res.text)['data']['zork_inst_id']
                # 插入成功后记录下id
                insert_server[baseinfo['servicetag']] = json.loads(res.text)['data']['zork_inst_id']
                print "insert_server:",insert_server
            else:
                print json.loads(res.text)['msg']

            # 硬件配置信息
            harewareConf = device['harewareConf']
            # cpu
            cpuInfo = harewareConf['cpuInfo']
            cpu = cpuInfo['cpu']
            for idx in range(len(cpu)):
                cpu_i = cpu[idx]
                # new_data = {
                #     "zork_obj_id": "cpu",
                #     "zork_inst_name": baseinfo['devicename']+" cpu"+str(idx),
                #     "model": cpu_i['type'],
                #     "frequency": cpu_i['frequency'],
                #     "index": cpu_i['index']
                # }
                new_data = {
                    "zork_obj_id": "zork_attachment_cpu",
                    "zork_inst_name": baseinfo['devicename']+" cpu"+str(idx),
                    "type": cpu_i['type'],
                    "mhz": cpu_i['frequency'],
                    "manufacturer": cpu_i['manufacturer'],
                    "part_number": cpu_i['partnumber']
                }
                res = requests.post(url=add_inst_url, headers=new_headers, data=json.dumps(new_data))
                print (json.dumps(res.text, sort_keys=True, indent=4, ensure_ascii=False))
                if json.loads(res.text)['code'] == 0:
                    print json.loads(res.text)['data']['zork_inst_id']
                    # cpu id
                    cpu_id = json.loads(res.text)['data']['zork_inst_id']
                    # 建立关系
                    # new_data = {
                    #     "zork_obj_id": "server",
                    #     "zork_inst_id": insert_server.get(baseinfo['servicetag']),
                    #     "zork_asst_obj_id": "cpu",
                    #     "zork_asst_inst_id": cpu_id,
                    #     "zork_obj_asst_id": "server_group_cpu"
                    # }
                    new_data = {
                        "zork_obj_id": "zork_server",
                        "zork_inst_id": insert_server.get(baseinfo['servicetag']),
                        "zork_asst_obj_id": "zork_attachment_cpu",
                        "zork_asst_inst_id": cpu_id,
                        "zork_obj_asst_id": "zork_server_group_zork_attachment_cpu"
                    }
                    res = requests.post(url=add_inst_asst_url, headers=new_headers, data=json.dumps(new_data))
                    print (json.dumps(res.text, sort_keys=True, indent=4, ensure_ascii=False))
                else:
                    print json.loads(res.text)['msg']
    else: 
        print "fail:", json.loads(resp.text)['message']

# 全量写入到cmdb
def post2cmdb():
    # 添加宿主机信息到cmdb
    add_vc_server()
    # 添加虚拟机信息到cmdb
    add_vc_vm()


# Start program
if __name__ == "__main__":
    # add_yx_server()
    post2cmdb()
    main()